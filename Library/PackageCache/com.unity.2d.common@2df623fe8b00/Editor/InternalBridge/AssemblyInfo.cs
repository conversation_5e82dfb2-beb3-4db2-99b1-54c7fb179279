using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.2D.Animation.Editor")]
[assembly: InternalsVisibleTo("Unity.2D.Animation.Tests.EditorTests")]
[assembly: InternalsVisibleTo("Unity.2D.SpriteShape.Editor")]
[assembly: InternalsVisibleTo("Unity.2D.SpriteShape.EditorTests")]
[assembly: InternalsVisibleTo("Unity.2D.PsdImporter.Editor")]
[assembly: InternalsVisibleTo("Unity.2D.Muse.Editor")]
[assembly: InternalsVisibleTo("Unity.2D.Muse.Tests.EditorTests")]
