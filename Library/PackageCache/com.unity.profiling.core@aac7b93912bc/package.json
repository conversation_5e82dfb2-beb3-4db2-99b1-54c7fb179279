{"name": "com.unity.profiling.core", "displayName": "Unity Profiling Core API", "version": "1.0.2", "unity": "2020.1", "description": "The Unity Profiling Core package provides an API for code instrumentation markup, and for profiling statistic collection.", "keywords": ["profiler", "profiling", "api"], "upm": {"changelog": "### Changed,- Removed 'preview package' paragraph from documentation."}, "relatedPackages": {"com.unity.profiling.core.tests": "1.0.2"}, "upmCi": {"footprint": "3f39f7351890affc1c40e4b0b9a67372e078404d"}, "repository": {"url": "https://github.cds.internal.unity3d.com/unity/profiler.git", "type": "git", "revision": "2189ba14439d76a4083f59fae87163b4bdfd49c2"}, "_fingerprint": "aac7b93912bc5df5fe06b04ff1b758493cdc2346"}