# About Unity Profiling Core
Use the Unity Profiling Core package to add contextual information to the [Unity Profiler](https://docs.unity3d.com/Manual/Profiler.html) captures. You can use the [Scripting APIs](https://docs.unity3d.com/Packages/com.unity.profiling.core@latest?subfolder=/api/index.html) provided with the Unity Profiling Core package to add a string or number to a Profiler sample or pass custom data to the Profiler data stream to later use in the Editor.

## Installation
To install this package, follow the instructions in the [Package Manager documentation](https://docs.unity3d.com/Manual/upm-ui-install.html). The Unity Profiling Core package is not discoverable in the Package Manager UI because it is a core package. Therefore the recommended installation method is to [add the package by name](https://docs.unity3d.com/2021.2/Documentation/Manual/upm-ui-quick.html), which is `com.unity.profiling.core`. If you are using Unity 2021.2.0a5 or newer, you can also install it via [this link](com.unity3d.kharma:upmpackage/com.unity.profiling.core).

![Install Package By Name](images/install_package_by_name.png)<br/>*Install package by name.*

## Requirements
This version of Unity Profiling Core is compatible with the following versions of the Unity Editor:

* 2020.1 and later

## Helpful links
If you are new to Unity Profiling Core, or have a question after reading the documentation, you can:

* Watch the [Tutorials](https://learn.unity.com/tutorial/introduction-to-the-profiler) here.
* Join our [support forum](https://forum.unity.com/forums/profiler-previews.199/).
