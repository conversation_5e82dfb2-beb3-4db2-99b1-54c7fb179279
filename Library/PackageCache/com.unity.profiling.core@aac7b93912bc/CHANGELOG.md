# Changelog
All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.0.2] - 2022-03-10
### Changed
- Removed 'preview package' paragraph from documentation. 

## [1.0.1] - 2021-09-03
### Changed
- Added direct install link to the documentation.

## [1.0.0] - 2021-08-30
### Fixed
- Made structs layout to be compatible with Burst in Release Players.

## [1.0.0-pre.1] - 2021-02-16
### Fixed
- Fixed ProfilerMarker with 3 parameters being configured as for 2 parameters.

### Removed
- ProfilerCounter constructor with MarkerFlags.

## [0.2.1-preview.1] - 2020-08-25
### Fixed
- ProfilerMarker and ProfilerCounter usage in release Player builds.

## [0.2.0-preview.1] - 2020-08-18
### Added
- ProfilerCounter and ProfilerCounterValue API.
- Profiler counters documentation and usage guide.

## [0.1.0-preview.1] - 2020-05-28
### Added
- This is the first release of *Unity Profiling Core Package*.
- ProfilerMarker API with metadata support.
