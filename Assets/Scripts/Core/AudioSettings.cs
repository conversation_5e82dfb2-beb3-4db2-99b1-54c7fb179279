using UnityEngine;
using System;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 音频设置管理器
    /// 处理音频设置的保存、加载和应用
    /// </summary>
    [System.Serializable]
    public class AudioSettings
    {
        [Header("音量设置")]
        [Range(0f, 1f)] public float masterVolume = 1f;
        [Range(0f, 1f)] public float musicVolume = 1f;
        [Range(0f, 1f)] public float sfxVolume = 1f;
        [Range(0f, 1f)] public float uiVolume = 1f;
        
        [Header("音频质量设置")]
        public AudioQuality audioQuality = AudioQuality.High;
        public bool enableAudio3D = true;
        public bool enableReverb = true;
        public bool enableEcho = false;
        
        [Header("移动设备优化")]
        public bool enableMobileOptimization = true;
        public bool reduceSampleRate = false;
        public bool enableAudioCompression = true;
        
        [Header("用户偏好")]
        public bool muteOnFocusLost = true;
        public bool enableHapticFeedback = true;
        public bool enableSubtitles = false;
        
        // 设置键名常量
        private const string MASTER_VOLUME_KEY = "AudioSettings_MasterVolume";
        private const string MUSIC_VOLUME_KEY = "AudioSettings_MusicVolume";
        private const string SFX_VOLUME_KEY = "AudioSettings_SFXVolume";
        private const string UI_VOLUME_KEY = "AudioSettings_UIVolume";
        private const string AUDIO_QUALITY_KEY = "AudioSettings_AudioQuality";
        private const string ENABLE_AUDIO_3D_KEY = "AudioSettings_EnableAudio3D";
        private const string ENABLE_REVERB_KEY = "AudioSettings_EnableReverb";
        private const string ENABLE_ECHO_KEY = "AudioSettings_EnableEcho";
        private const string ENABLE_MOBILE_OPTIMIZATION_KEY = "AudioSettings_EnableMobileOptimization";
        private const string REDUCE_SAMPLE_RATE_KEY = "AudioSettings_ReduceSampleRate";
        private const string ENABLE_AUDIO_COMPRESSION_KEY = "AudioSettings_EnableAudioCompression";
        private const string MUTE_ON_FOCUS_LOST_KEY = "AudioSettings_MuteOnFocusLost";
        private const string ENABLE_HAPTIC_FEEDBACK_KEY = "AudioSettings_EnableHapticFeedback";
        private const string ENABLE_SUBTITLES_KEY = "AudioSettings_EnableSubtitles";
        
        // 事件
        public static event Action<AudioSettings> OnSettingsChanged;
        
        /// <summary>
        /// 音频质量枚举
        /// </summary>
        public enum AudioQuality
        {
            Low,
            Medium,
            High,
            Ultra
        }
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AudioSettings()
        {
            SetDefaults();
        }
        
        /// <summary>
        /// 设置默认值
        /// </summary>
        public void SetDefaults()
        {
            masterVolume = 1f;
            musicVolume = 0.8f;
            sfxVolume = 1f;
            uiVolume = 1f;
            audioQuality = AudioQuality.High;
            enableAudio3D = true;
            enableReverb = true;
            enableEcho = false;
            enableMobileOptimization = Application.isMobilePlatform;
            reduceSampleRate = Application.isMobilePlatform;
            enableAudioCompression = true;
            muteOnFocusLost = true;
            enableHapticFeedback = Application.isMobilePlatform;
            enableSubtitles = false;
        }
        
        /// <summary>
        /// 保存设置到PlayerPrefs
        /// </summary>
        public void SaveSettings()
        {
            PlayerPrefs.SetFloat(MASTER_VOLUME_KEY, masterVolume);
            PlayerPrefs.SetFloat(MUSIC_VOLUME_KEY, musicVolume);
            PlayerPrefs.SetFloat(SFX_VOLUME_KEY, sfxVolume);
            PlayerPrefs.SetFloat(UI_VOLUME_KEY, uiVolume);
            PlayerPrefs.SetInt(AUDIO_QUALITY_KEY, (int)audioQuality);
            PlayerPrefs.SetInt(ENABLE_AUDIO_3D_KEY, enableAudio3D ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_REVERB_KEY, enableReverb ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_ECHO_KEY, enableEcho ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_MOBILE_OPTIMIZATION_KEY, enableMobileOptimization ? 1 : 0);
            PlayerPrefs.SetInt(REDUCE_SAMPLE_RATE_KEY, reduceSampleRate ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_AUDIO_COMPRESSION_KEY, enableAudioCompression ? 1 : 0);
            PlayerPrefs.SetInt(MUTE_ON_FOCUS_LOST_KEY, muteOnFocusLost ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_HAPTIC_FEEDBACK_KEY, enableHapticFeedback ? 1 : 0);
            PlayerPrefs.SetInt(ENABLE_SUBTITLES_KEY, enableSubtitles ? 1 : 0);
            
            PlayerPrefs.Save();
            
            // 触发设置变更事件
            OnSettingsChanged?.Invoke(this);
        }
        
        /// <summary>
        /// 从PlayerPrefs加载设置
        /// </summary>
        public void LoadSettings()
        {
            masterVolume = PlayerPrefs.GetFloat(MASTER_VOLUME_KEY, masterVolume);
            musicVolume = PlayerPrefs.GetFloat(MUSIC_VOLUME_KEY, musicVolume);
            sfxVolume = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, sfxVolume);
            uiVolume = PlayerPrefs.GetFloat(UI_VOLUME_KEY, uiVolume);
            audioQuality = (AudioQuality)PlayerPrefs.GetInt(AUDIO_QUALITY_KEY, (int)audioQuality);
            enableAudio3D = PlayerPrefs.GetInt(ENABLE_AUDIO_3D_KEY, enableAudio3D ? 1 : 0) == 1;
            enableReverb = PlayerPrefs.GetInt(ENABLE_REVERB_KEY, enableReverb ? 1 : 0) == 1;
            enableEcho = PlayerPrefs.GetInt(ENABLE_ECHO_KEY, enableEcho ? 1 : 0) == 1;
            enableMobileOptimization = PlayerPrefs.GetInt(ENABLE_MOBILE_OPTIMIZATION_KEY, enableMobileOptimization ? 1 : 0) == 1;
            reduceSampleRate = PlayerPrefs.GetInt(REDUCE_SAMPLE_RATE_KEY, reduceSampleRate ? 1 : 0) == 1;
            enableAudioCompression = PlayerPrefs.GetInt(ENABLE_AUDIO_COMPRESSION_KEY, enableAudioCompression ? 1 : 0) == 1;
            muteOnFocusLost = PlayerPrefs.GetInt(MUTE_ON_FOCUS_LOST_KEY, muteOnFocusLost ? 1 : 0) == 1;
            enableHapticFeedback = PlayerPrefs.GetInt(ENABLE_HAPTIC_FEEDBACK_KEY, enableHapticFeedback ? 1 : 0) == 1;
            enableSubtitles = PlayerPrefs.GetInt(ENABLE_SUBTITLES_KEY, enableSubtitles ? 1 : 0) == 1;
            
            // 触发设置变更事件
            OnSettingsChanged?.Invoke(this);
        }
        
        /// <summary>
        /// 应用设置到音频系统
        /// </summary>
        public void ApplySettings()
        {
            // 应用音量设置
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(masterVolume);
                AudioManager.Instance.SetMusicVolume(musicVolume);
                AudioManager.Instance.SetSFXVolume(sfxVolume);
            }
            
            // 应用音频质量设置
            ApplyAudioQualitySettings();
            
            // 应用移动设备优化
            if (enableMobileOptimization)
            {
                ApplyMobileOptimizations();
            }
        }
        
        /// <summary>
        /// 应用音频质量设置
        /// </summary>
        private void ApplyAudioQualitySettings()
        {
            AudioConfiguration audioConfig = AudioSettings.GetConfiguration();
            
            switch (audioQuality)
            {
                case AudioQuality.Low:
                    audioConfig.sampleRate = 22050;
                    audioConfig.speakerMode = AudioSpeakerMode.Mono;
                    break;
                case AudioQuality.Medium:
                    audioConfig.sampleRate = 44100;
                    audioConfig.speakerMode = AudioSpeakerMode.Stereo;
                    break;
                case AudioQuality.High:
                    audioConfig.sampleRate = 48000;
                    audioConfig.speakerMode = AudioSpeakerMode.Stereo;
                    break;
                case AudioQuality.Ultra:
                    audioConfig.sampleRate = 96000;
                    audioConfig.speakerMode = AudioSpeakerMode.Stereo;
                    break;
            }
            
            if (reduceSampleRate && Application.isMobilePlatform)
            {
                audioConfig.sampleRate = Mathf.Min(audioConfig.sampleRate, 44100);
            }
            
            AudioSettings.Reset(audioConfig);
        }
        
        /// <summary>
        /// 应用移动设备优化
        /// </summary>
        private void ApplyMobileOptimizations()
        {
            if (Application.isMobilePlatform)
            {
                // 降低音频缓冲区大小以减少延迟
                AudioConfiguration config = AudioSettings.GetConfiguration();
                config.dspBufferSize = 512;
                AudioSettings.Reset(config);
                
                // 启用音频压缩
                if (enableAudioCompression)
                {
                    // 这里可以添加音频压缩相关的设置
                }
            }
        }
        
        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            SetDefaults();
            SaveSettings();
            ApplySettings();
        }
        
        /// <summary>
        /// 验证设置有效性
        /// </summary>
        public bool ValidateSettings()
        {
            bool isValid = true;
            
            // 验证音量范围
            if (masterVolume < 0f || masterVolume > 1f)
            {
                Debug.LogWarning("主音量超出有效范围 (0-1)");
                masterVolume = Mathf.Clamp01(masterVolume);
                isValid = false;
            }
            
            if (musicVolume < 0f || musicVolume > 1f)
            {
                Debug.LogWarning("音乐音量超出有效范围 (0-1)");
                musicVolume = Mathf.Clamp01(musicVolume);
                isValid = false;
            }
            
            if (sfxVolume < 0f || sfxVolume > 1f)
            {
                Debug.LogWarning("音效音量超出有效范围 (0-1)");
                sfxVolume = Mathf.Clamp01(sfxVolume);
                isValid = false;
            }
            
            if (uiVolume < 0f || uiVolume > 1f)
            {
                Debug.LogWarning("UI音量超出有效范围 (0-1)");
                uiVolume = Mathf.Clamp01(uiVolume);
                isValid = false;
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 获取设置摘要
        /// </summary>
        public string GetSettingsSummary()
        {
            return $"音频设置摘要:\n" +
                   $"主音量: {masterVolume:P0}\n" +
                   $"音乐音量: {musicVolume:P0}\n" +
                   $"音效音量: {sfxVolume:P0}\n" +
                   $"UI音量: {uiVolume:P0}\n" +
                   $"音频质量: {audioQuality}\n" +
                   $"3D音频: {(enableAudio3D ? "启用" : "禁用")}\n" +
                   $"混响: {(enableReverb ? "启用" : "禁用")}\n" +
                   $"移动优化: {(enableMobileOptimization ? "启用" : "禁用")}";
        }
        
        /// <summary>
        /// 复制设置
        /// </summary>
        public AudioSettings Clone()
        {
            AudioSettings clone = new AudioSettings();
            clone.masterVolume = this.masterVolume;
            clone.musicVolume = this.musicVolume;
            clone.sfxVolume = this.sfxVolume;
            clone.uiVolume = this.uiVolume;
            clone.audioQuality = this.audioQuality;
            clone.enableAudio3D = this.enableAudio3D;
            clone.enableReverb = this.enableReverb;
            clone.enableEcho = this.enableEcho;
            clone.enableMobileOptimization = this.enableMobileOptimization;
            clone.reduceSampleRate = this.reduceSampleRate;
            clone.enableAudioCompression = this.enableAudioCompression;
            clone.muteOnFocusLost = this.muteOnFocusLost;
            clone.enableHapticFeedback = this.enableHapticFeedback;
            clone.enableSubtitles = this.enableSubtitles;
            
            return clone;
        }
        
        /// <summary>
        /// 比较两个设置是否相等
        /// </summary>
        public bool Equals(AudioSettings other)
        {
            if (other == null) return false;
            
            return masterVolume == other.masterVolume &&
                   musicVolume == other.musicVolume &&
                   sfxVolume == other.sfxVolume &&
                   uiVolume == other.uiVolume &&
                   audioQuality == other.audioQuality &&
                   enableAudio3D == other.enableAudio3D &&
                   enableReverb == other.enableReverb &&
                   enableEcho == other.enableEcho &&
                   enableMobileOptimization == other.enableMobileOptimization &&
                   reduceSampleRate == other.reduceSampleRate &&
                   enableAudioCompression == other.enableAudioCompression &&
                   muteOnFocusLost == other.muteOnFocusLost &&
                   enableHapticFeedback == other.enableHapticFeedback &&
                   enableSubtitles == other.enableSubtitles;
        }
    }
}
