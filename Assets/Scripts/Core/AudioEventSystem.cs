using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 音频事件系统
    /// 处理游戏中各种事件触发的音频播放
    /// </summary>
    public class AudioEventSystem : MonoBehaviour
    {
        [Header("音频事件配置")]
        [SerializeField] private AudioEventConfig audioEventConfig;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 音频管理器引用
        private IAudioManager audioManager;
        
        // 事件监听器
        private Dictionary<string, System.Action> eventListeners;
        
        // 延迟播放队列
        private Queue<DelayedAudioEvent> delayedAudioQueue;
        
        /// <summary>
        /// 延迟音频事件
        /// </summary>
        private struct DelayedAudioEvent
        {
            public string audioName;
            public float delay;
            public float timestamp;
            
            public DelayedAudioEvent(string audioName, float delay)
            {
                this.audioName = audioName;
                this.delay = delay;
                this.timestamp = Time.time;
            }
            
            public bool IsReady => Time.time >= timestamp + delay;
        }
        
        private void Awake()
        {
            InitializeEventSystem();
        }
        
        private void Start()
        {
            RegisterAudioManager();
            RegisterGameEvents();
        }
        
        private void Update()
        {
            ProcessDelayedAudioEvents();
        }
        
        /// <summary>
        /// 初始化事件系统
        /// </summary>
        private void InitializeEventSystem()
        {
            eventListeners = new Dictionary<string, System.Action>();
            delayedAudioQueue = new Queue<DelayedAudioEvent>();
            
            // 创建默认配置
            if (audioEventConfig == null)
            {
                audioEventConfig = ScriptableObject.CreateInstance<AudioEventConfig>();
            }
        }
        
        /// <summary>
        /// 注册音频管理器
        /// </summary>
        private void RegisterAudioManager()
        {
            if (GameManager.Instance != null)
            {
                audioManager = GameManager.Instance.GetAudioManager();
            }
            
            if (audioManager == null)
            {
                audioManager = AudioManager.Instance;
            }
        }
        
        /// <summary>
        /// 注册游戏事件
        /// </summary>
        private void RegisterGameEvents()
        {
            // 游戏状态事件
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStart += () => PlayAudioEvent("GameStart");
                GameManager.Instance.OnGamePause += () => PlayAudioEvent("GamePause");
                GameManager.Instance.OnGameResume += () => PlayAudioEvent("GameResume");
                GameManager.Instance.OnGameOver += () => PlayAudioEvent("GameOver");
            }
            
            // 分数系统事件
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnScoreChanged += (score) => PlayAudioEvent("ScoreChanged");
                ScoreManager.Instance.OnComboChanged += (combo) => 
                {
                    if (combo > 1)
                    {
                        PlayAudioEvent($"Combo{Mathf.Min(combo, 10)}");
                    }
                };
                ScoreManager.Instance.OnMilestoneReached += (milestone) => PlayAudioEvent("MilestoneReached");
            }
            
            // 成就系统事件
            if (AchievementManager.Instance != null)
            {
                AchievementManager.Instance.OnAchievementUnlocked += (achievement) => PlayAudioEvent("AchievementUnlocked");
            }
        }
        
        /// <summary>
        /// 播放音频事件
        /// </summary>
        public void PlayAudioEvent(string eventName)
        {
            if (audioEventConfig == null || audioManager == null) return;
            
            AudioEventConfig.AudioEvent audioEvent = audioEventConfig.GetAudioEvent(eventName);
            if (audioEvent != null)
            {
                if (audioEvent.delay > 0)
                {
                    PlayDelayedAudioEvent(audioEvent.audioName, audioEvent.delay);
                }
                else
                {
                    audioManager.PlaySoundEffect(audioEvent.audioName);
                }
                
                if (showDebugInfo)
                {
                    Debug.Log($"播放音频事件: {eventName} -> {audioEvent.audioName}");
                }
            }
            else if (showDebugInfo)
            {
                Debug.LogWarning($"音频事件未找到: {eventName}");
            }
        }
        
        /// <summary>
        /// 播放延迟音频事件
        /// </summary>
        public void PlayDelayedAudioEvent(string audioName, float delay)
        {
            delayedAudioQueue.Enqueue(new DelayedAudioEvent(audioName, delay));
        }
        
        /// <summary>
        /// 处理延迟音频事件
        /// </summary>
        private void ProcessDelayedAudioEvents()
        {
            int queueCount = delayedAudioQueue.Count;
            for (int i = 0; i < queueCount; i++)
            {
                DelayedAudioEvent delayedEvent = delayedAudioQueue.Dequeue();
                
                if (delayedEvent.IsReady)
                {
                    audioManager?.PlaySoundEffect(delayedEvent.audioName);
                }
                else
                {
                    delayedAudioQueue.Enqueue(delayedEvent);
                }
            }
        }
        
        /// <summary>
        /// 播放UI音效
        /// </summary>
        public void PlayUISound(string soundName)
        {
            audioManager?.PlaySoundEffect(soundName);
        }
        
        /// <summary>
        /// 播放角色音效
        /// </summary>
        public void PlayCharacterSound(string soundName)
        {
            audioManager?.PlaySoundEffect(soundName);
        }
        
        /// <summary>
        /// 播放敌人音效
        /// </summary>
        public void PlayEnemySound(string soundName)
        {
            audioManager?.PlaySoundEffect(soundName);
        }
        
        /// <summary>
        /// 播放环境音效
        /// </summary>
        public void PlayEnvironmentSound(string soundName)
        {
            audioManager?.PlaySoundEffect(soundName);
        }
        
        /// <summary>
        /// 播放收集品音效
        /// </summary>
        public void PlayCollectibleSound(string soundName)
        {
            audioManager?.PlaySoundEffect(soundName);
        }
        
        /// <summary>
        /// 停止所有音效
        /// </summary>
        public void StopAllSoundEffects()
        {
            // 清空延迟队列
            delayedAudioQueue.Clear();
            
            // 这里可以添加停止所有音效的逻辑
            if (showDebugInfo)
            {
                Debug.Log("停止所有音效");
            }
        }
        
        /// <summary>
        /// 设置音频事件配置
        /// </summary>
        public void SetAudioEventConfig(AudioEventConfig config)
        {
            audioEventConfig = config;
        }
        
        #region 静态实例管理
        
        private static AudioEventSystem instance;
        public static AudioEventSystem Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<AudioEventSystem>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("AudioEventSystem");
                        instance = go.AddComponent<AudioEventSystem>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 音频事件配置
    /// </summary>
    [CreateAssetMenu(fileName = "AudioEventConfig", menuName = "Mobile Scrolling Game/Audio/Audio Event Config")]
    public class AudioEventConfig : ScriptableObject
    {
        [System.Serializable]
        public class AudioEvent
        {
            [Header("事件信息")]
            public string eventName;
            public string audioName;
            
            [Header("播放设置")]
            [Range(0f, 5f)] public float delay = 0f;
            public bool randomizeDelay = false;
            [Range(0f, 1f)] public float delayVariation = 0.1f;
            
            [Header("条件设置")]
            public bool enableConditions = false;
            public float cooldownTime = 0f;
            public int maxPlaysPerSecond = 10;
            
            [Header("描述")]
            [TextArea(2, 4)] public string description;
            
            // 运行时数据
            [System.NonSerialized] private float lastPlayTime;
            [System.NonSerialized] private int playsThisSecond;
            [System.NonSerialized] private float currentSecond;
            
            /// <summary>
            /// 检查是否可以播放
            /// </summary>
            public bool CanPlay()
            {
                if (!enableConditions) return true;
                
                float currentTime = Time.time;
                
                // 检查冷却时间
                if (cooldownTime > 0 && currentTime - lastPlayTime < cooldownTime)
                {
                    return false;
                }
                
                // 检查每秒播放次数限制
                if (maxPlaysPerSecond > 0)
                {
                    int currentSecondInt = Mathf.FloorToInt(currentTime);
                    if (currentSecondInt != currentSecond)
                    {
                        currentSecond = currentSecondInt;
                        playsThisSecond = 0;
                    }
                    
                    if (playsThisSecond >= maxPlaysPerSecond)
                    {
                        return false;
                    }
                }
                
                return true;
            }
            
            /// <summary>
            /// 记录播放
            /// </summary>
            public void RecordPlay()
            {
                lastPlayTime = Time.time;
                playsThisSecond++;
            }
            
            /// <summary>
            /// 获取实际延迟时间
            /// </summary>
            public float GetActualDelay()
            {
                if (randomizeDelay)
                {
                    return delay + Random.Range(-delayVariation, delayVariation);
                }
                return delay;
            }
        }
        
        [Header("音频事件列表")]
        [SerializeField] private List<AudioEvent> audioEvents = new List<AudioEvent>();
        
        // 缓存字典
        private Dictionary<string, AudioEvent> eventCache;
        private bool isCacheInitialized = false;
        
        /// <summary>
        /// 初始化缓存
        /// </summary>
        private void InitializeCache()
        {
            if (isCacheInitialized) return;
            
            eventCache = new Dictionary<string, AudioEvent>();
            
            foreach (var audioEvent in audioEvents)
            {
                if (audioEvent != null && !string.IsNullOrEmpty(audioEvent.eventName))
                {
                    eventCache[audioEvent.eventName] = audioEvent;
                }
            }
            
            isCacheInitialized = true;
        }
        
        /// <summary>
        /// 获取音频事件
        /// </summary>
        public AudioEvent GetAudioEvent(string eventName)
        {
            InitializeCache();
            
            if (eventCache.TryGetValue(eventName, out AudioEvent audioEvent))
            {
                return audioEvent;
            }
            
            return null;
        }
        
        /// <summary>
        /// 添加音频事件
        /// </summary>
        public void AddAudioEvent(AudioEvent audioEvent)
        {
            if (audioEvent != null && !string.IsNullOrEmpty(audioEvent.eventName))
            {
                audioEvents.Add(audioEvent);
                
                if (isCacheInitialized)
                {
                    eventCache[audioEvent.eventName] = audioEvent;
                }
            }
        }
        
        /// <summary>
        /// 移除音频事件
        /// </summary>
        public bool RemoveAudioEvent(string eventName)
        {
            AudioEvent eventToRemove = audioEvents.Find(e => e.eventName == eventName);
            if (eventToRemove != null)
            {
                audioEvents.Remove(eventToRemove);
                
                if (isCacheInitialized)
                {
                    eventCache.Remove(eventName);
                }
                
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            eventCache?.Clear();
            isCacheInitialized = false;
        }
    }
}
